"""
Configuration settings for the Wiz Aroma Delivery Bot.
Contains all constants, environment variables, and configuration settings.
"""

import os
import ast
import logging
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import our custom logging utility
from src.utils.logging_utils import get_logger

# Get the logger
logger = get_logger()

# Default log level
DEFAULT_LOG_LEVEL = logging.INFO

# Test mode for development (set to True to bypass token validation)
TEST_MODE = False

# Configure timeouts and retries for Telegram API
RETRY_ON_ERROR = True
CONNECT_TIMEOUT = 3
READ_TIMEOUT = 5

# Configure watchdog interval (in seconds)
WATCHDOG_INTERVAL = 30

# Load all sensitive tokens from environment variables
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_BOT_TOKEN = os.getenv("ADMIN_BOT_TOKEN")
FINANCE_BOT_TOKEN = os.getenv("FINANCE_BOT_TOKEN")
MAINTENANCE_BOT_TOKEN = os.getenv("MAINTENANCE_BOT_TOKEN")
MANAGEMENT_BOT_TOKEN = os.getenv("MANAGEMENT_BOT_TOKEN")
ORDER_TRACK_BOT_TOKEN = os.getenv("ORDER_TRACK_BOT_TOKEN")
DELIVERY_BOT_TOKEN = os.getenv("DELIVERY_BOT_TOKEN")

# Define required tokens for validation
REQUIRED_TOKENS = {
    'BOT_TOKEN': BOT_TOKEN,
    'ADMIN_BOT_TOKEN': ADMIN_BOT_TOKEN,
    'FINANCE_BOT_TOKEN': FINANCE_BOT_TOKEN,
    'MAINTENANCE_BOT_TOKEN': MAINTENANCE_BOT_TOKEN,
    'MANAGEMENT_BOT_TOKEN': MANAGEMENT_BOT_TOKEN,
    'ORDER_TRACK_BOT_TOKEN': ORDER_TRACK_BOT_TOKEN,
    'DELIVERY_BOT_TOKEN': DELIVERY_BOT_TOKEN
}

# Validate essential environment variables if not in test mode
missing_tokens = [name for name, value in REQUIRED_TOKENS.items() if not value]

if not TEST_MODE and missing_tokens:
    logger.error("Missing required bot tokens in .env file!")
    logger.error(f"Missing tokens: {', '.join(missing_tokens)}")
    logger.error("Please check your .env file and make sure all tokens are set.")
    logger.error("You can copy .env.example to .env and fill in the actual values.")
    sys.exit(1)
elif TEST_MODE and missing_tokens:
    logger.warning(f"Running in TEST_MODE with placeholder tokens for: {', '.join(missing_tokens)}")
    # Use placeholder tokens in test mode if not provided
    if not BOT_TOKEN:
        BOT_TOKEN = "test_bot_token"
    if not ADMIN_BOT_TOKEN:
        ADMIN_BOT_TOKEN = "test_admin_bot_token"
    if not FINANCE_BOT_TOKEN:
        FINANCE_BOT_TOKEN = "test_finance_bot_token"
    if not MAINTENANCE_BOT_TOKEN:
        MAINTENANCE_BOT_TOKEN = "test_maintenance_bot_token"
    if not MANAGEMENT_BOT_TOKEN:
        MANAGEMENT_BOT_TOKEN = "test_management_bot_token"
    if not ORDER_TRACK_BOT_TOKEN:
        ORDER_TRACK_BOT_TOKEN = "test_order_track_bot_token"
    if not DELIVERY_BOT_TOKEN:
        DELIVERY_BOT_TOKEN = "test_delivery_bot_token"

# Access control for specialized bots - Load from environment variables
try:
    # Get system admin ID - REQUIRED for security
    system_admin_id = os.getenv("SYSTEM_ADMIN_ID")
    if not system_admin_id:
        logger.error("SYSTEM_ADMIN_ID environment variable is required but not set!")
        logger.error("Please set SYSTEM_ADMIN_ID in your .env file")
        sys.exit(1)

    system_admin_fallback = int(system_admin_id)
    ORDER_TRACK_BOT_AUTHORIZED_IDS = ast.literal_eval(
        os.getenv("ORDER_TRACK_BOT_AUTHORIZED_IDS", f"[{system_admin_fallback}]")
    )
except (SyntaxError, ValueError) as e:
    logger.error(f"Error parsing ORDER_TRACK_BOT_AUTHORIZED_IDS: {e}")
    logger.error("Please check the format of ORDER_TRACK_BOT_AUTHORIZED_IDS in your .env file")
    sys.exit(1)

# Delivery bot authorization - now managed dynamically via Firebase
# These are fallback admin IDs when Firebase is unavailable
try:
    # Use the already validated system admin ID
    DELIVERY_BOT_AUTHORIZED_IDS = ast.literal_eval(
        os.getenv("DELIVERY_BOT_AUTHORIZED_IDS", f"[{system_admin_fallback}]")
    )
except (SyntaxError, ValueError) as e:
    logger.error(f"Error parsing DELIVERY_BOT_AUTHORIZED_IDS: {e}")
    logger.error("Please check the format of DELIVERY_BOT_AUTHORIZED_IDS in your .env file")
    sys.exit(1)

# Chat IDs from .env file
try:
    ADMIN_CHAT_IDS = ast.literal_eval(
        os.getenv("ADMIN_CHAT_IDS", "[]")
    )  # Convert string list to actual list
    if not ADMIN_CHAT_IDS:
        logger.warning("No admin chat IDs configured!")
except (SyntaxError, ValueError) as e:
    logger.error(f"Error parsing ADMIN_CHAT_IDS: {e}")
    logger.error("Setting ADMIN_CHAT_IDS to empty list")
    ADMIN_CHAT_IDS = []

FINANCE_CHAT_ID = os.getenv("FINANCE_CHAT_ID")
if not FINANCE_CHAT_ID:
    logger.warning("No finance chat ID configured!")

# Management bot configuration
MANAGEMENT_CHAT_ID = os.getenv("MANAGEMENT_CHAT_ID")
if not MANAGEMENT_CHAT_ID:
    logger.warning("No management chat ID configured!")

# Maintenance bot configuration
MAINTENANCE_CHAT_ID = os.getenv("MAINTENANCE_CHAT_ID")
if not MAINTENANCE_CHAT_ID:
    logger.warning(
        "No maintenance chat ID configured. Using the first admin ID if available."
    )
    MAINTENANCE_CHAT_ID = ADMIN_CHAT_IDS[0] if ADMIN_CHAT_IDS else None

# Email Configuration from .env file
EMAIL_ADDRESS = os.getenv("EMAIL_ADDRESS")
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD")

if not EMAIL_ADDRESS or not EMAIL_PASSWORD:
    logger.warning(
        "Email credentials not configured! Email notifications will not work."
    )

# Payment Information from .env file
# Telebirr
TELEBIRR_PHONE = os.getenv("TELEBIRR_PHONE")
TELEBIRR_NAME = os.getenv("TELEBIRR_NAME")

# CBE Bank
CBE_ACCOUNT_NUMBER = os.getenv("CBE_ACCOUNT_NUMBER")
CBE_ACCOUNT_NAME = os.getenv("CBE_ACCOUNT_NAME")

# BOA Bank
BOA_ACCOUNT_NUMBER = os.getenv("BOA_ACCOUNT_NUMBER")
BOA_ACCOUNT_NAME = os.getenv("BOA_ACCOUNT_NAME")


# Contact Information
SUPPORT_PHONE_1 = os.getenv("SUPPORT_PHONE_1")
SUPPORT_PHONE_2 = os.getenv("SUPPORT_PHONE_2")
SUPPORT_TELEGRAM = os.getenv("SUPPORT_TELEGRAM")

# Additional Contact Information
CUSTOMER_SERVICE_PHONE = os.getenv("CUSTOMER_SERVICE_PHONE")
CUSTOMER_SERVICE_EMAIL = os.getenv("CUSTOMER_SERVICE_EMAIL")
BUSINESS_EMAIL = os.getenv("BUSINESS_EMAIL")

# System Admin ID - Use the already validated value
SYSTEM_ADMIN_ID = system_admin_fallback

# Validate payment information
if not all(
    [
        TELEBIRR_PHONE,
        TELEBIRR_NAME,
        CBE_ACCOUNT_NUMBER,
        CBE_ACCOUNT_NAME,
        BOA_ACCOUNT_NUMBER,
        BOA_ACCOUNT_NAME,
    ]
):
    logger.warning("Some payment information is missing in .env file!")

# Data directory
DATA_DIR = "data_files"

# File paths for persistent storage
POINTS_FILE = os.path.join(DATA_DIR, "user_points.json")
ORDER_HISTORY_FILE = os.path.join(DATA_DIR, "user_order_history.json")
USER_NAMES_FILE = os.path.join(DATA_DIR, "user_names.json")
USER_PHONE_NUMBERS_FILE = os.path.join(DATA_DIR, "user_phone_numbers.json")
USER_EMAILS_FILE = os.path.join(DATA_DIR, "user_emails.json")

# Data storage file paths
AREAS_FILE = os.path.join(DATA_DIR, "areas.json")
RESTAURANTS_FILE = os.path.join(DATA_DIR, "restaurants.json")
MENUS_FILE = os.path.join(DATA_DIR, "menus.json")
DELIVERY_LOCATIONS_FILE = os.path.join(DATA_DIR, "delivery_locations.json")
DELIVERY_FEES_FILE = os.path.join(DATA_DIR, "delivery_fees.json")
FAVORITE_ORDERS_FILE = os.path.join(DATA_DIR, "favorite_orders.json")
CURRENT_ORDERS_FILE = os.path.join(DATA_DIR, "current_orders.json")
ORDER_STATUS_FILE = os.path.join(DATA_DIR, "order_status.json")
PENDING_ADMIN_REVIEWS_FILE = os.path.join(DATA_DIR, "pending_admin_reviews.json")
ADMIN_REMARKS_FILE = os.path.join(DATA_DIR, "admin_remarks.json")
AWAITING_RECEIPT_FILE = os.path.join(DATA_DIR, "awaiting_receipt.json")
DELIVERY_LOCATIONS_TEMP_FILE = os.path.join(DATA_DIR, "delivery_locations_temp.json")
USER_ORDER_COUNTS_FILE = os.path.join(DATA_DIR, "user_order_counts.json")
CURRENT_ORDER_NUMBERS_FILE = os.path.join(DATA_DIR, "current_order_numbers.json")

# Sample restaurants by area for demo/presentation purposes
# Using formal naming conventions: Sample/Test + category + numbers
restaurants = {
    "Sample Area 1": {
        1: {"name": "Test Restaurant 1"},
        2: {"name": "Test Restaurant 2"},
    },
    "Sample Area 2": {
        3: {"name": "Test Restaurant 3"},
        4: {"name": "Test Restaurant 4"},
    },
    "Sample Area 3": {
        5: {"name": "Test Restaurant 5"},
        6: {"name": "Test Restaurant 6"},
    },
}

# Sample delivery fees for demo/presentation purposes
# Using formal naming conventions: Sample/Test + category + numbers
delivery_fees = {
    "Sample Area 1": {
        "Test Location 1": 25,
        "Test Location 2": 30,
        "Test Location 3": 35,
        "Test Location 4": 40,
        "Test Location 5": 45,
    },
    "Sample Area 2": {
        "Test Location 1": 30,
        "Test Location 2": 25,
        "Test Location 3": 30,
        "Test Location 4": 35,
        "Test Location 5": 40,
    },
    "Sample Area 3": {
        "Test Location 1": 40,
        "Test Location 2": 35,
        "Test Location 3": 25,
        "Test Location 4": 30,
        "Test Location 5": 35,
    },
}

# Menus are now loaded dynamically from src.data.menus
